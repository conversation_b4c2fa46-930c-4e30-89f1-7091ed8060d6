import { ScoringService } from '../scoring/scoring-service';
import { AnalysisResult, OverallSummary } from '../types';
export class SummaryService {
  private readonly weights = {
    EXPERIENCE: 0.25,
    SKILLS: 0.25,
    TITLE: 0.1,
    PROJECTS: 0.1,
    EDUCATION: 0.08,
    CERTIFICATIONS: 0.08,
    PUBLICATIONS: 0.08,
    AWARDS: 0.03,
    INTERESTS: 0.03,
  };

  private scoringService: ScoringService;

  constructor(scoringService: ScoringService) {
    this.scoringService = scoringService;
  }

  public calculateOverallSummary(
    experienceAnalysis: AnalysisResult['experience'],
    skillsAnalysis: AnalysisResult['skills'],
    titleAnalysis: AnalysisResult['title'],
    projectsAnalysis: AnalysisResult['projects'],
    educationAnalysis: AnalysisResult['education'],
    certificationsAnalysis: AnalysisResult['certifications'],
    awardsAnalysis: AnalysisResult['awards'],
    interestsAnalysis: AnalysisResult['interests'],
    publicationsAnalysis: AnalysisResult['publications']
  ): OverallSummary {
    const experienceScore =
      this.scoringService.calculateExperienceScore(experienceAnalysis);
    const skillsScore = skillsAnalysis.summary.coveragePercentage;
    const titleScore = titleAnalysis.confidence * 100;
    const projectsScore =
      this.scoringService.calculateProjectsScore(projectsAnalysis);
    const educationScore =
      this.scoringService.calculateEducationScore(educationAnalysis);
    const certificationsScore = this.scoringService.calculateCertificationScore(
      certificationsAnalysis
    );
    const awardsScore = this.scoringService.calculateAwardScore(awardsAnalysis);
    const interestsScore =
      this.scoringService.calculateInterestScore(interestsAnalysis);
    const publicationsScore =
      this.scoringService.calculatePublicationsScore(publicationsAnalysis);

    const totalScore = Math.round(
      experienceScore * this.weights.EXPERIENCE +
        skillsScore * this.weights.SKILLS +
        titleScore * this.weights.TITLE +
        projectsScore * this.weights.PROJECTS +
        educationScore * this.weights.EDUCATION +
        certificationsScore * this.weights.CERTIFICATIONS +
        publicationsScore * this.weights.PUBLICATIONS +
        awardsScore * this.weights.AWARDS +
        interestsScore * this.weights.INTERESTS
    );

    const matchQuality = this.calculateMatchQuality(
      experienceScore,
      skillsScore,
      titleAnalysis.confidence,
      projectsScore,
      educationScore,
      certificationsScore,
      awardsScore,
      interestsScore,
      publicationsScore
    );

    const isRecommended = this.shouldRecommendResume(
      matchQuality,
      titleAnalysis.confidence,
      skillsScore
    );

    const strengths = this.identifyStrengths(
      experienceAnalysis,
      skillsAnalysis,
      titleAnalysis,
      projectsAnalysis,
      educationAnalysis,
      certificationsAnalysis,
      awardsAnalysis,
      interestsAnalysis,
      publicationsAnalysis
    );
    const improvements = this.identifyImprovements(
      experienceAnalysis,
      skillsAnalysis,
      titleAnalysis,
      projectsAnalysis,
      educationAnalysis,
      certificationsAnalysis,
      awardsAnalysis,
      interestsAnalysis,
      publicationsAnalysis
    );

    return {
      totalScore,
      matchQuality,
      strengths,
      improvements,
      isRecommended,
    };
  }

  private calculateMatchQuality(
    experienceScore: number,
    skillsCoverage: number,
    titleConfidence: number,
    projectsScore: number,
    educationScore: number,
    certificationsScore: number,
    awardsScore: number,
    interestsScore: number,
    publicationsScore: number
  ): 'poor' | 'fair' | 'good' | 'excellent' {
    const overallScore =
      experienceScore * this.weights.EXPERIENCE +
      skillsCoverage * this.weights.SKILLS +
      titleConfidence * 100 * this.weights.TITLE +
      projectsScore * this.weights.PROJECTS +
      educationScore * this.weights.EDUCATION +
      certificationsScore * this.weights.CERTIFICATIONS +
      awardsScore * this.weights.AWARDS +
      publicationsScore * this.weights.PUBLICATIONS +
      interestsScore * this.weights.INTERESTS;

    if (overallScore >= 80) return 'excellent';
    if (overallScore >= 65) return 'good';
    if (overallScore >= 45) return 'fair';

    return 'poor';
  }

  private shouldRecommendResume(
    matchQuality: 'poor' | 'fair' | 'good' | 'excellent',
    titleConfidence: number,
    skillsCoverage: number
  ): boolean {
    return (
      matchQuality === 'excellent' ||
      matchQuality === 'good' ||
      (matchQuality === 'fair' &&
        (titleConfidence >= 0.7 || skillsCoverage >= 60))
    );
  }

  private identifyStrengths(
    experienceAnalysis: AnalysisResult['experience'],
    skillsAnalysis: AnalysisResult['skills'],
    titleAnalysis: AnalysisResult['title'],
    projectsAnalysis: AnalysisResult['projects'],
    educationAnalysis: AnalysisResult['education'],
    certificationsAnalysis: AnalysisResult['certifications'],
    awardsAnalysis: AnalysisResult['awards'],
    interestsAnalysis: AnalysisResult['interests'],
    publicationsAnalysis: AnalysisResult['publications']
  ): string[] {
    const strengths: string[] = [];

    if (experienceAnalysis.summary.averageRelevanceScore >= 4) {
      strengths.push('Highly relevant work experience');
    }
    if (experienceAnalysis.summary.highRelevanceCount >= 2) {
      strengths.push('Multiple high-quality experiences');
    }

    if (skillsAnalysis.summary.coveragePercentage >= 70) {
      strengths.push('Excellent skill coverage');
    }
    if (skillsAnalysis.summary.directMatchCount >= 5) {
      strengths.push('Strong direct skill matches');
    }

    if (titleAnalysis.confidence >= 0.8) {
      strengths.push('Well-aligned job title');
    }

    if (this.scoringService.calculateProjectsScore(projectsAnalysis) >= 70) {
      strengths.push('Strong relevant project experience');
    }

    if (this.scoringService.calculateEducationScore(educationAnalysis) >= 70) {
      strengths.push('Highly relevant educational background');
    }

    if (
      this.scoringService.calculateCertificationScore(certificationsAnalysis) >=
      70
    ) {
      strengths.push('Valuable certifications aligned with the role');
    }

    if (this.scoringService.calculateAwardScore(awardsAnalysis) >= 70) {
      strengths.push('Prestigious awards demonstrating excellence');
    }

    if (this.scoringService.calculateInterestScore(interestsAnalysis) >= 70) {
      strengths.push('Interests align well with company culture');
    }

    if (
      this.scoringService.calculatePublicationsScore(publicationsAnalysis) >= 70
    ) {
      strengths.push('Relevant publications aligned with the role');
    }

    return strengths;
  }

  private identifyImprovements(
    experienceAnalysis: AnalysisResult['experience'],
    skillsAnalysis: AnalysisResult['skills'],
    titleAnalysis: AnalysisResult['title'],
    projectsAnalysis: AnalysisResult['projects'],
    educationAnalysis: AnalysisResult['education'],
    certificationsAnalysis: AnalysisResult['certifications'],
    awardsAnalysis: AnalysisResult['awards'],
    interestsAnalysis: AnalysisResult['interests'],
    publicationsAnalysis: AnalysisResult['publications']
  ): string[] {
    const improvements: string[] = [];

    if (experienceAnalysis.summary.averageRelevanceScore < 3) {
      improvements.push('Highlight more relevant work experience');
    }

    if (skillsAnalysis.summary.coveragePercentage < 50) {
      improvements.push('Develop missing technical skills');
    }
    if (skillsAnalysis.summary.missingSkillCount > 5) {
      improvements.push('Address key skill gaps');
    }

    if (titleAnalysis.suggestedTitle) {
      improvements.push(
        `Consider updating title to: ${titleAnalysis.suggestedTitle}`
      );
    }

    if (this.scoringService.calculateProjectsScore(projectsAnalysis) < 50) {
      improvements.push(
        'Include more projects aligned with the job requirements'
      );
    }

    if (this.scoringService.calculateEducationScore(educationAnalysis) < 50) {
      improvements.push('Emphasize relevant coursework or academic projects');
    }

    if (
      this.scoringService.calculateCertificationScore(certificationsAnalysis) <
      50
    ) {
      improvements.push('Pursue or highlight relevant certifications');
    }

    if (this.scoringService.calculateAwardScore(awardsAnalysis) < 50) {
      improvements.push('Showcase any relevant awards or recognitions');
    }

    if (this.scoringService.calculateInterestScore(interestsAnalysis) < 50) {
      improvements.push(
        'Consider highlighting interests that may align with company culture'
      );
    }

    if (
      this.scoringService.calculatePublicationsScore(publicationsAnalysis) < 50
    ) {
      improvements.push('Highlight publications relevant to the target role');
    }

    return improvements;
  }
}
