import { AwardAnalysis, AwardAnalysisOptions } from './awards/types';
import {
  CertificationAnalysis,
  CertificationAnalysisOptions,
} from './certifications/types';
import { EducationAnalysis, EducationAnalysisOptions } from './education/types';
import {
  ExperienceAnalysis,
  ExperienceAnalysisOptions,
} from './experience/types';
import { InterestAnalysis, InterestAnalysisOptions } from './interests/types';
import { ProjectAnalysis, ProjectAnalysisOptions } from './projects/types';
import {
  PublicationAnalysis,
  PublicationAnalysisOptions,
} from './publications/types';
import { SkillAnalysis, SkillAnalysisOptions } from './skills/types';
import { TitleAnalysis, TitleAnalysisOptions } from './title/utils';

export type AnalysisOptions = {
  experience?: ExperienceAnalysisOptions;
  skills?: SkillAnalysisOptions;
  title?: TitleAnalysisOptions;
  projects?: ProjectAnalysisOptions;
  education?: EducationAnalysisOptions;
  certifications?: CertificationAnalysisOptions;
  awards?: AwardAnalysisOptions;
  interests?: InterestAnalysisOptions;
  publications?: PublicationAnalysisOptions;
  includeSourceDetails?: boolean;
  timeoutMs?: number;
};

export type AnalysisResult = {
  experience: ExperienceAnalysis;
  skills: SkillAnalysis;
  title: TitleAnalysis;
  projects: ProjectAnalysis;
  education: EducationAnalysis;
  certifications: CertificationAnalysis;
  awards: AwardAnalysis;
  interests: InterestAnalysis;
  publications: PublicationAnalysis;
  overallSummary: {
    totalScore: number;
    matchQuality: 'poor' | 'fair' | 'good' | 'excellent';
    strengths: string[];
    improvements: string[];
    isRecommended: boolean;
  };
};

export type OverallSummary = AnalysisResult['overallSummary'];
